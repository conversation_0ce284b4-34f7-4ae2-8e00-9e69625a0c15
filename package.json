{"name": "@rugby/go", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "type": "module", "scripts": {"ng": "ng", "start": "nx run app:serve", "build": "nx build", "watch": "nx build --watch --configuration development", "test": "nx test", "lint": "nx lint"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@capacitor/app": "7.0.1", "@capacitor/core": "7.2.0", "@capacitor/haptics": "7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/angular": "^8.5.7", "@ngrx/signals": "^19.2.0", "ionicons": "^7.4.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.0", "@angular-devkit/core": "^19.0.0", "@angular-devkit/schematics": "^19.0.0", "@angular-eslint/eslint-plugin": "^19.0.0", "@angular-eslint/eslint-plugin-template": "^19.0.0", "@angular-eslint/template-parser": "^19.0.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@angular/language-service": "^19.0.0", "@capacitor/cli": "7.2.0", "@ionic/angular-toolkit": "^12.0.0", "@nx/angular": "21.0.1", "@nx/eslint-plugin": "21.0.1", "@nx/eslint": "21.0.1", "@nx/jest": "21.0.1", "@nx/js": "21.0.1", "@nx/workspace": "21.0.1", "@schematics/angular": "^19.0.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/jasmine": "~5.1.0", "@types/jest": "^29.5.12", "@types/node": "18.16.9", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "@typescript-eslint/utils": "^8.19.0", "angular-eslint": "^19.2.0", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-preset-angular": "~14.4.0", "nx": "21.0.1", "prettier": "^3.0.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "~5.6.3"}, "description": "An Ionic project"}