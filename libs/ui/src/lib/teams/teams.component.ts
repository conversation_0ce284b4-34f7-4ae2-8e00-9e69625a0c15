import { Component, inject, OnInit } from '@angular/core';
import { teamsEvents, TeamsStore } from '@rugby/store';
import {
    IonLabel,
    IonThumbnail,
    IonImg,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonText,
    IonIcon,
    IonGrid,
    IonRow,
    IonCol,
    IonAvatar,
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import {
    flagOutline,
    peopleOutline,
    homeOutline,
    calendarOutline,
} from 'ionicons/icons';
import { injectDispatch } from '@ngrx/signals/events';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'rugby-teams',
    standalone: true,
    styleUrls: ['./teams.component.css'],
    template: `
        <!--@if (teamsStore.status() === 'busy') {
            <div class="ion-padding ion-text-center">
                <ion-spinner name="circular"></ion-spinner>
                <ion-text>Loading teams...</ion-text>
            </div>
        }-->

        @if (teamsStore.status() === 'error') {
            <div class="ion-padding">
                <ion-text color="danger"
                    >Error: {{ teamsStore.error() }}</ion-text
                >
            </div>
        }

        @if (teamsStore.status() === 'success') {
            <ion-grid>
                <ion-row>
                    @for (team of teamsStore.teams(); track team.id) {
                        <ion-col size="12" size-md="6" size-lg="4">
                            <ion-card class="team-card">
                                <ion-card-header>
                                    <div class="team-header">
                                        <ion-thumbnail>
                                            <ion-img
                                                [src]="team.logo"
                                                [alt]="team.name + ' logo'"
                                            />
                                        </ion-thumbnail>
                                        <ion-card-title>{{
                                            team.name
                                        }}</ion-card-title>
                                    </div>
                                </ion-card-header>
                                <ion-card-content>
                                    <div class="team-info">
                                        @if (team.national) {
                                            <div class="info-item">
                                                <ion-icon
                                                    name="flag-outline"
                                                    color="primary"
                                                ></ion-icon>
                                                <span class="info-text"
                                                    >National Team</span
                                                >
                                            </div>
                                        }
                                        <div class="info-item">
                                            <ion-icon
                                                name="calendar-outline"
                                                color="secondary"
                                            ></ion-icon>
                                            <span class="info-text"
                                                >Founded:
                                                {{ team.founded }}</span
                                            >
                                        </div>
                                        <div class="info-item">
                                            <ion-icon
                                                name="home-outline"
                                                color="tertiary"
                                            ></ion-icon>
                                            <span class="info-text"
                                                >{{ team.arena.name }} ({{
                                                    team.arena.capacity
                                                }}
                                                seats)</span
                                            >
                                        </div>
                                        <div class="info-item country-info">
                                            <ion-icon
                                                name="people-outline"
                                                color="success"
                                            ></ion-icon>
                                            <span class="info-text">{{
                                                team.country.name
                                            }}</span>
                                        </div>
                                    </div>
                                </ion-card-content>
                            </ion-card>
                        </ion-col>
                    } @empty {
                        <ion-col size="12">
                            <ion-card>
                                <ion-card-content>
                                    <div class="ion-text-center">
                                        <ion-label>No teams found</ion-label>
                                    </div>
                                </ion-card-content>
                            </ion-card>
                        </ion-col>
                    }
                </ion-row>
            </ion-grid>
        }
    `,
    imports: [
        IonLabel,
        IonThumbnail,
        IonImg,
        IonCard,
        IonCardHeader,
        IonCardTitle,
        IonCardContent,
        IonText,
        IonIcon,
        IonGrid,
        IonRow,
        IonCol,
    ],
})
export class TeamsComponent implements OnInit {
    teamsStore = inject(TeamsStore);
    readonly dispatch = injectDispatch(teamsEvents);

    constructor() {
        // Add Ionicons
        addIcons({
            'flag-outline': flagOutline,
            'people-outline': peopleOutline,
            'home-outline': homeOutline,
            'calendar-outline': calendarOutline,
        });
    }

    ngOnInit() {
        // Load default teams (South Africa, Currie Cup, 2023)

        this.dispatch.fetchTeams({
            selectedCountryId: '20',
            selectedLeagueId: '37',
            selectedSeason: '2023',
        });
    }
}
