import { Component, inject, OnInit } from '@angular/core';
import { leaguesEvents, LeaguesStore } from '@rugby/store';
import {
    IonSpinner,
    IonList,
    IonItem,
    IonLabel,
    IonThumbnail,
    IonImg,
    IonText,
    IonChip,
    IonBadge,
} from '@ionic/angular/standalone';
import { injectDispatch } from '@ngrx/signals/events';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'rugby-leagues',
    standalone: true,
    styleUrls: ['./leagues.component.css'],
    template: `
        <!--@if (leaguesStore.status() === 'busy') {
            <div class="ion-padding">
                <ion-spinner name="circular"></ion-spinner>
                <ion-text>Loading leagues...</ion-text>
            </div>
        }-->

        @if (leaguesStore.status() === 'error') {
            <div class="ion-padding">
                <ion-text color="danger"
                    >Error: {{ leaguesStore.error() }}</ion-text
                >
            </div>
        }

        @if (leaguesStore.status() === 'success') {
            <!--<ion-card>
                <ion-card-header>
                    <ion-card-title>Rugby Leagues</ion-card-title>
                </ion-card-header>

                <ion-card-content>-->
            <ion-list>
                @for (league of leaguesStore.leagues(); track league.id) {
                    <ion-item>
                        <ion-thumbnail slot="start">
                            <ion-img
                                [src]="league.logo"
                                [alt]="league.name + ' logo'"
                            />
                        </ion-thumbnail>

                        <ion-label>
                            <h2>{{ league.name }}</h2>
                            <p>
                                <ion-chip
                                    color="primary"
                                    outline="true"
                                    size="small"
                                >
                                    <ion-label>{{ league.type }}</ion-label>
                                </ion-chip>
                                <ion-badge color="secondary">{{
                                    league.country.name
                                }}</ion-badge>
                            </p>
                        </ion-label>
                    </ion-item>
                } @empty {
                    <ion-item>
                        <ion-label>No leagues found</ion-label>
                    </ion-item>
                }
            </ion-list>
            <!--</ion-card-content>
            </ion-card>-->
        }
    `,
    imports: [
        IonSpinner,
        IonList,
        IonItem,
        IonLabel,
        IonThumbnail,
        IonText,
        IonChip,
        IonBadge,
        IonImg,
    ],
})
export class LeaguesComponent implements OnInit {
    readonly leaguesStore = inject(LeaguesStore);
    readonly dispatch = injectDispatch(leaguesEvents);

    ngOnInit() {
        this.dispatch.fetchLeagues({ countryId: '20', season: '2023' });
    }
}
