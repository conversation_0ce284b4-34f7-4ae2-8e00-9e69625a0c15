import { Injectable, inject } from '@angular/core';
import {
    patchState,
    signalStore,
    withComputed,
    withMethods,
    withState,
} from '@ngrx/signals';
import { League } from './models/league.model';
import { computed } from '@angular/core';
import { LeaguesApiService } from './services/leagues-api.service';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, of } from 'rxjs';
import { initialState } from './state/state';
import { withLeaguesReducer } from './reducer/reducer';
import { withLeaguesPersistenceFeature } from './effects/persistence.feature';

/**
 * Store for managing Rugby leagues data
 */

export const LeaguesStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),
    withLeaguesReducer(),
    withLeaguesPersistenceFeature(),
    withComputed((state) => ({
        /**
         * Returns all available league types from the leagues
         */
        leagueTypes: computed(() => {
            const types = new Set<string>();
            state.leagues().forEach((league) => types.add(league.type));
            return Array.from(types);
        }),

        /**
         * Returns all available countries from the leagues
         */
        countries: computed(() => {
            const countries = new Map<
                number,
                { id: number; name: string; code: string; flag: string }
            >();
            state.leagues().forEach((league) => {
                const country = league.country;
                countries.set(country.id, {
                    id: country.id,
                    name: country.name,
                    code: country.code,
                    flag: country.flag,
                });
            });
            return Array.from(countries.values());
        }),
    })),
    withMethods((store, leaguesApiService = inject(LeaguesApiService)) => ({
        /**
         * Sets the leagues in the store
         */
        setLeagues(leagues: League[]) {
            patchState(store, { leagues, loading: false, error: null });
        },

        /**
         * Sets the loading state
         */
        setLoading(loading: boolean) {
            patchState(store, { loading });
        },

        /**
         * Sets an error message
         */
        setError(error: string) {
            patchState(store, { error, loading: false });
        },

        /**
         * Sets the selected country ID and season filters
         */
        setFilters(countryId: string, season: string) {
            patchState(store, {
                selectedCountryId: countryId,
                selectedSeason: season,
            });
        },

        /**
         * Fetches leagues from the API based on the selected filters
         */
        fetchLeagues: rxMethod<void>(
            pipe(
                tap(() => {
                    patchState(store, { loading: true, error: null });
                }),
                switchMap(() => {
                    const countryId = store.selectedCountryId() || '20'; // Default to South Africa (20)
                    const type = store.selectedType() || 'cup'; // Default to cup
                    const season = store.selectedSeason() || '2022'; // Default to cup

                    return leaguesApiService
                        .getLeagues(countryId, type, season)
                        .pipe(
                            tap((response) => {
                                patchState(store, {
                                    leagues: response.response,
                                    loading: false,
                                    error: null,
                                });
                            }),
                            catchError((error) => {
                                const errorMessage =
                                    error.message || 'Failed to fetch leagues';
                                patchState(store, {
                                    error: errorMessage,
                                    loading: false,
                                });
                                return of(null);
                            }),
                        );
                }),
            ),
        ),

        /**
         * Loads leagues for South Africa in 2023 (default)
         */
        loadDefaultLeagues() {
            this.setFilters('20', '2023');
            this.fetchLeagues();
        },
    })),
);
