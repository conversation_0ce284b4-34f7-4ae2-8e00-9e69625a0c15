import { TestBed } from '@angular/core/testing';
import { signalStore, withState } from '@ngrx/signals';
import { withLeaguesReducer } from './reducer';
import { leaguesEvents, leaguesApiEvents } from '../events/events';
import { LeaguesState, initialState } from '../state/state';
import { League } from '../models/league.model';

describe('withLeaguesReducer', () => {
  // Mock data
  const mockLeagues: League[] = [
    {
      id: 37,
      name: 'Currie Cup',
      type: 'Cup',
      logo: 'https://media.api-sports.io/rugby/leagues/37.png',
      country: {
        id: 20,
        name: 'South-Africa',
        code: 'ZA',
        flag: 'https://media.api-sports.io/flags/za.svg'
      },
      seasons: [
        {
          season: 2023,
          current: true,
          start: '2023-01-01',
          end: '2023-12-31'
        }
      ]
    },
    {
      id: 38,
      name: 'Super Rugby',
      type: 'League',
      logo: 'https://media.api-sports.io/rugby/leagues/38.png',
      country: {
        id: 20,
        name: 'South-Africa',
        code: 'ZA',
        flag: 'https://media.api-sports.io/flags/za.svg'
      },
      seasons: [
        {
          season: 2023,
          current: true,
          start: '2023-02-01',
          end: '2023-11-30'
        }
      ]
    }
  ];

  let store: any;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    
    // Create a test store with the reducer
    const TestStore = signalStore(
      withState<LeaguesState>(initialState),
      withLeaguesReducer()
    );
    
    store = TestBed.inject(TestStore);
  });

  it('should create the reducer feature', () => {
    const feature = withLeaguesReducer();
    expect(feature).toBeDefined();
    expect(typeof feature).toBe('function');
  });

  describe('leaguesEvents.fetchLeagues', () => {
    it('should set loading to true and update filters', () => {
      const payload = { countryId: '20', season: '2023' };
      
      // Dispatch the event
      store.dispatch(leaguesEvents.fetchLeagues({ payload }));
      
      // Verify state changes
      expect(store.selectedCountryId()).toBe('20');
      expect(store.selectedSeason()).toBe('2023');
      expect(store.loading()).toBe(true);
      expect(store.error()).toBeNull();
    });

    it('should clear previous error when fetching leagues', () => {
      // Set initial error state
      store.dispatch(leaguesApiEvents.leaguesLoadFailed({ 
        payload: { error: 'Previous error' } 
      }));
      expect(store.error()).toBe('Previous error');
      
      // Dispatch fetch event
      store.dispatch(leaguesEvents.fetchLeagues({ 
        payload: { countryId: '20', season: '2023' } 
      }));
      
      // Verify error is cleared
      expect(store.error()).toBeNull();
      expect(store.loading()).toBe(true);
    });
  });

  describe('leaguesApiEvents.leaguesLoaded', () => {
    it('should set loading to false and update leagues', () => {
      // Set initial loading state
      store.dispatch(leaguesEvents.fetchLeagues({ 
        payload: { countryId: '20', season: '2023' } 
      }));
      expect(store.loading()).toBe(true);
      
      // Dispatch leagues loaded event
      store.dispatch(leaguesApiEvents.leaguesLoaded({ 
        payload: { leagues: mockLeagues } 
      }));
      
      // Verify state changes
      expect(store.loading()).toBe(false);
      expect(store.leagues()).toEqual(mockLeagues);
    });

    it('should handle empty leagues array', () => {
      store.dispatch(leaguesApiEvents.leaguesLoaded({ 
        payload: { leagues: [] } 
      }));
      
      expect(store.loading()).toBe(false);
      expect(store.leagues()).toEqual([]);
    });
  });

  describe('leaguesApiEvents.leaguesLoadFailed', () => {
    it('should set loading to false and update error', () => {
      // Set initial loading state
      store.dispatch(leaguesEvents.fetchLeagues({ 
        payload: { countryId: '20', season: '2023' } 
      }));
      expect(store.loading()).toBe(true);
      
      const errorMessage = 'Failed to load leagues';
      
      // Dispatch leagues load failed event
      store.dispatch(leaguesApiEvents.leaguesLoadFailed({ 
        payload: { error: errorMessage } 
      }));
      
      // Verify state changes
      expect(store.loading()).toBe(false);
      expect(store.error()).toBe(errorMessage);
    });

    it('should handle different error messages', () => {
      const customError = 'Network timeout error';
      
      store.dispatch(leaguesApiEvents.leaguesLoadFailed({ 
        payload: { error: customError } 
      }));
      
      expect(store.loading()).toBe(false);
      expect(store.error()).toBe(customError);
    });
  });

  describe('state transitions', () => {
    it('should handle complete fetch -> success flow', () => {
      // Initial state
      expect(store.loading()).toBe(false);
      expect(store.error()).toBeNull();
      expect(store.leagues()).toEqual([]);
      
      // Start fetch
      store.dispatch(leaguesEvents.fetchLeagues({ 
        payload: { countryId: '20', season: '2023' } 
      }));
      
      expect(store.loading()).toBe(true);
      expect(store.selectedCountryId()).toBe('20');
      expect(store.selectedSeason()).toBe('2023');
      
      // Complete fetch successfully
      store.dispatch(leaguesApiEvents.leaguesLoaded({ 
        payload: { leagues: mockLeagues } 
      }));
      
      expect(store.loading()).toBe(false);
      expect(store.leagues()).toEqual(mockLeagues);
    });

    it('should handle complete fetch -> error flow', () => {
      // Start fetch
      store.dispatch(leaguesEvents.fetchLeagues({ 
        payload: { countryId: '20', season: '2023' } 
      }));
      
      expect(store.loading()).toBe(true);
      
      // Complete fetch with error
      const errorMessage = 'API error';
      store.dispatch(leaguesApiEvents.leaguesLoadFailed({ 
        payload: { error: errorMessage } 
      }));
      
      expect(store.loading()).toBe(false);
      expect(store.error()).toBe(errorMessage);
      expect(store.leagues()).toEqual([]); // Should remain unchanged
    });

    it('should handle multiple fetch operations', () => {
      // First fetch
      store.dispatch(leaguesEvents.fetchLeagues({ 
        payload: { countryId: '20', season: '2023' } 
      }));
      
      store.dispatch(leaguesApiEvents.leaguesLoaded({ 
        payload: { leagues: mockLeagues } 
      }));
      
      expect(store.leagues()).toEqual(mockLeagues);
      
      // Second fetch with different parameters
      store.dispatch(leaguesEvents.fetchLeagues({ 
        payload: { countryId: '21', season: '2024' } 
      }));
      
      expect(store.selectedCountryId()).toBe('21');
      expect(store.selectedSeason()).toBe('2024');
      expect(store.loading()).toBe(true);
      expect(store.error()).toBeNull();
    });
  });
});
