import { Injectable, inject } from '@angular/core';
import {
    patchState,
    signalStore,
    withComputed,
    withMethods,
    withState,
} from '@ngrx/signals';
import { League } from './models/league.model';
import { computed } from '@angular/core';
import { LeaguesApiService } from './services/leagues-api.service';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, of } from 'rxjs';
import { initialState } from './state/state';
import { withLeaguesReducer } from './reducer/reducer';
import { withLeaguesEffects } from './effects/persistence.feature';

/**
 * Store for managing Rugby leagues data
 */

export const LeaguesStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),
    withLeaguesReducer(),
    withLeaguesEffects(),
    withComputed((state) => ({
        /**
         * Returns all available league types from the leagues
         */
        leagueTypes: computed(() => {
            const types = new Set<string>();
            state.leagues().forEach((league) => types.add(league.type));
            return Array.from(types);
        }),

        /**
         * Returns all available countries from the leagues
         */
        countries: computed(() => {
            const countries = new Map<
                number,
                { id: number; name: string; code: string; flag: string }
            >();
            state.leagues().forEach((league) => {
                const country = league.country;
                countries.set(country.id, {
                    id: country.id,
                    name: country.name,
                    code: country.code,
                    flag: country.flag,
                });
            });
            return Array.from(countries.values());
        }),
    })),
    withMethods((store) => ({
        /**
         * Sets the selected country ID and season filters
         */
        setFilters(countryId: string, season: string) {
            patchState(store, {
                selectedCountryId: countryId,
                selectedSeason: season,
            });
        },
    })),
);
