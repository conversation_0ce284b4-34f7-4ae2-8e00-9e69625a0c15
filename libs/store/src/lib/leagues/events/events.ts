import { type } from '@ngrx/signals';
import { eventGroup } from '@ngrx/signals/events';
import { League } from '../models/league.model';

export const leaguesEvents = eventGroup({
    source: 'Leagues',
    events: {
        fetchLeagues: type<{ countryId: string; season: string }>(),
    },
});

export const leaguesApiEvents = eventGroup({
    source: 'LeaguesApi',
    events: {
        leaguesLoadFailed: type<{ error: string }>(),
        leaguesLoaded: type<{ leagues: League[] }>(),
    },
});
