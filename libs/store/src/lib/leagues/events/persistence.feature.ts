import { patchState, signalStoreFeature, type } from '@ngrx/signals';
import { LeaguesState } from './state/state';
import { Events, withEffects } from '@ngrx/signals/events';
import { leaguesApiEvents, leaguesEvents } from './events/events';
import { catchError, exhaustMap, map, of } from 'rxjs';
import { inject } from '@angular/core';
import { LeaguesApiService } from './services/leagues-api.service';

export function withLeaguesPersistenceFeature<_>() {
    return signalStoreFeature(
        {
            state: type<LeaguesState>(),
        },
        withEffects(
            (
                store,
                events = inject(Events),
                api = inject(LeaguesApiService),
            ) => ({
                loadSettings$: events.on(leaguesEvents.fetchLeagues).pipe(
                    exhaustMap(() => {
                        const countryId = store.selectedCountryId() || '20'; // Default to South Africa (20)
                        const type = store.selectedType() || 'cup'; // Default to cup
                        const season = store.selectedSeason() || '2022'; // Default to cup

                        return api.getLeagues(countryId, type, season).pipe(
                            map((response) => {
                                return leaguesApiEvents.leaguesLoaded({
                                    leagues: response.response,
                                });
                            }),
                            catchError((error) => {
                                return of(
                                    leaguesApiEvents.leaguesLoadFailed(error),
                                );
                            }),
                        );
                    }),
                ),
            }),
        ),
    );
}
