import {
    patchState,
    signalStore,
    withComputed,
    withMethods,
    withState,
} from '@ngrx/signals';
import { Team } from '../models/team.model';
import { computed } from '@angular/core';
import { initialState } from '../state/state';
import { withTeamsReducer } from '../reducer/reducer';
import { withTeamsEffects } from '../effects/effects';

/**
 * Store for managing Rugby teams data
 */
export const TeamsStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),
    withTeamsReducer(),
    withTeamsEffects(),
    withComputed((state) => ({
        /**
         * Returns all available arenas from the teams
         */
        arenas: computed(() => {
            const arenas = new Map<
                string,
                { name: string; capacity: number; location: string }
            >();
            state.teams().forEach((team: Team) => {
                const arena = team.arena;
                arenas.set(arena.name, {
                    name: arena.name,
                    capacity: arena.capacity,
                    location: arena.location,
                });
            });
            return Array.from(arenas.values());
        }),

        /**
         * Returns all available countries from the teams
         */
        countries: computed(() => {
            const countries = new Map<
                number,
                { id: number; name: string; code: string; flag: string }
            >();
            state.teams().forEach((team: Team) => {
                const country = team.country;
                countries.set(country.id, {
                    id: country.id,
                    name: country.name,
                    code: country.code,
                    flag: country.flag,
                });
            });
            return Array.from(countries.values());
        }),
    })),
    withMethods((store) => ({
        /**
         * Sets the selected country ID, league ID, and season filters
         */
        setFilters(countryId: string, leagueId: string, season: string) {
            patchState(store, {
                selectedCountryId: countryId,
                selectedLeagueId: leagueId,
                selectedSeason: season,
            });
        },
    })),
);
