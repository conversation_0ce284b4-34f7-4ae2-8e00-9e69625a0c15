import { TestBed } from '@angular/core/testing';
import { TeamsStore } from './teams.store';
import { TeamsApiService } from '../services/teams-api.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';
import { Team, TeamsApiResponse } from '../models/team.model';

describe('TeamsStore', () => {
    let store: any;
    let apiService: TeamsApiService;

    const mockTeams: Team[] = [
        {
            id: 294,
            name: 'Bulls',
            logo: 'https://media.api-sports.io/rugby/teams/294.png',
            national: false,
            founded: 1997,
            arena: {
                name: 'Loftus Versfeld Stadium',
                capacity: 51762,
                location: 'Pretoria, South Africa',
            },
            country: {
                id: 20,
                name: 'South-Africa',
                code: 'ZA',
                flag: 'https://media.api-sports.io/flags/za.svg',
            },
        },
        {
            id: 295,
            name: '<PERSON><PERSON>tah<PERSON>',
            logo: 'https://media.api-sports.io/rugby/teams/295.png',
            national: false,
            founded: 2005,
            arena: {
                name: 'Free State Stadium',
                capacity: 46000,
                location: 'Bloemfontein, Free State, South Africa',
            },
            country: {
                id: 20,
                name: 'South-Africa',
                code: 'ZA',
                flag: 'https://media.api-sports.io/flags/za.svg',
            },
        },
    ];

    const mockApiResponse: TeamsApiResponse = {
        get: 'teams',
        parameters: {
            country_id: '20',
            league: '37',
            season: '2023',
        },
        errors: [],
        results: 2,
        response: mockTeams,
    };

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [TeamsApiService],
        });

        store = TestBed.inject(TeamsStore);
        apiService = TestBed.inject(TeamsApiService);
    });

    it('should be created', () => {
        expect(store).toBeTruthy();
    });

    it('should have initial state', () => {
        expect(store.teams()).toEqual([]);
        expect(store.status()).toBe('none');
        expect(store.error()).toBeNull();
        expect(store.selectedCountryId()).toBeNull();
        expect(store.selectedLeagueId()).toBeNull();
        expect(store.selectedSeason()).toBeNull();
    });

    describe('methods', () => {
        it('should set teams', () => {
            store.setTeams(mockTeams);
            expect(store.teams()).toEqual(mockTeams);
            expect(store.loading()).toBe(false);
            expect(store.error()).toBeNull();
        });

        it('should set loading state', () => {
            store.setLoading(true);
            expect(store.loading()).toBe(true);
        });

        it('should set error message', () => {
            const errorMessage = 'Failed to load teams';
            store.setError(errorMessage);
            expect(store.error()).toBe(errorMessage);
            expect(store.loading()).toBe(false);
        });

        it('should set filters', () => {
            store.setFilters('20', '37', '2023');
            expect(store.selectedCountryId()).toBe('20');
            expect(store.selectedLeagueId()).toBe('37');
            expect(store.selectedSeason()).toBe('2023');
        });

        it('should fetch teams successfully', () => {
            spyOn(apiService, 'getTeams').and.returnValue(of(mockApiResponse));

            store.setFilters('20', '37', '2023');
            store.fetchTeams();

            expect(apiService.getTeams).toHaveBeenCalledWith(
                '20',
                '37',
                '2023',
            );
            expect(store.teams()).toEqual(mockTeams);
            expect(store.loading()).toBe(false);
            expect(store.error()).toBeNull();
        });

        it('should handle error when fetching teams', () => {
            const error = new Error('API Error');
            spyOn(apiService, 'getTeams').and.returnValue(
                throwError(() => error),
            );

            store.setFilters('20', '37', '2023');
            store.fetchTeams();

            expect(apiService.getTeams).toHaveBeenCalledWith(
                '20',
                '37',
                '2023',
            );
            expect(store.teams()).toEqual([]);
            expect(store.loading()).toBe(false);
            expect(store.error()).toBe('API Error');
        });

        it('should load default teams', () => {
            spyOn(apiService, 'getTeams').and.returnValue(of(mockApiResponse));

            store.loadDefaultTeams();

            expect(store.selectedCountryId()).toBe('20');
            expect(store.selectedLeagueId()).toBe('37');
            expect(store.selectedSeason()).toBe('2023');
            expect(apiService.getTeams).toHaveBeenCalledWith(
                '20',
                '37',
                '2023',
            );
        });
    });

    describe('computed selectors', () => {
        beforeEach(() => {
            store.setTeams(mockTeams);
        });

        it('should get arenas', () => {
            expect(store.arenas().length).toBe(2);
            expect(store.arenas()[0].name).toBe('Loftus Versfeld Stadium');
            expect(store.arenas()[1].name).toBe('Free State Stadium');
        });

        it('should get countries', () => {
            expect(store.countries().length).toBe(1);
            expect(store.countries()[0].id).toBe(20);
            expect(store.countries()[0].name).toBe('South-Africa');
        });
    });
});
