import { signalStoreFeature, type } from '@ngrx/signals';
import { Events, withEffects } from '@ngrx/signals/events';
import { inject } from '@angular/core';
import { catchError, map, of, switchMap } from 'rxjs';
import { TeamsState } from '../state/state';
import { teamsApiEvents, teamsEvents } from '../events/events';
import { TeamsApiService } from '../services/teams-api.service';
import { HttpErrorResponse } from '@angular/common/http';

export function withTeamsEffects<_>() {
    return signalStoreFeature(
        {
            state: type<TeamsState>(),
        },
        withEffects(
            (
                store,
                events = inject(Events),
                api = inject(TeamsApiService),
            ) => ({
                loadSettings$: events.on(teamsEvents.fetchTeams).pipe(
                    switchMap(({ payload }) => {
                        const leagueId = payload.selectedLeagueId;
                        const countryId = payload.selectedCountryId;
                        const season = payload.selectedSeason;

                        // const countryId = store.selectedCountryId() || '20'; // Default to South Africa (20)
                        // const leagueId = store.selectedLeagueId() || '37'; // Default to Currie Cup (37)
                        // const season = store.selectedSeason() || '2023'; // Default to 2023 season

                        return api.getTeams(countryId, leagueId, season).pipe(
                            map((response) =>
                                teamsApiEvents.teamsLoaded({
                                    teams: response.response,
                                }),
                            ),
                            catchError((ex: unknown) => {
                                let msg = 'Unknown error has occurred';
                                if (ex instanceof HttpErrorResponse) {
                                    msg = ex.message;
                                }
                                return of(
                                    teamsApiEvents.teamsLoadFailed({
                                        error: msg,
                                    }),
                                );
                            }),
                        );
                    }),
                ),
            }),
        ),
    );
}
