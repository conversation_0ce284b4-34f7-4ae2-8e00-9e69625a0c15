import { eventGroup } from '@ngrx/signals/events';
import { type } from '@ngrx/signals';
import { Team } from '../models/team.model';

export const teamsEvents = eventGroup({
    source: 'Teams',
    events: {
        fetchTeams: type<{
            selectedCountryId: string;
            selectedLeagueId: string;
            selectedSeason: string;
        }>(),
    },
});

export const teamsApiEvents = eventGroup({
    source: 'TeamsApi',
    events: {
        teamsLoadFailed: type<{ error: string }>(),
        teamsLoaded: type<{ teams: Team[] }>(),
    },
});
