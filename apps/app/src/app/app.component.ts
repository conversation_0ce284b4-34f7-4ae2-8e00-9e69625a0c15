import { Component, computed, effect, inject } from '@angular/core';
import {
    IonA<PERSON>,
    IonLoading,
    IonRouterOutlet,
    LoadingController,
} from '@ionic/angular/standalone';
import { LeaguesStore, TeamsStore } from '@rugby/store';
import { toObservable } from '@angular/core/rxjs-interop';
import {
    combineLatest,
    combineLatestWith,
    distinctUntilChanged,
    startWith,
} from 'rxjs';

@Component({
    selector: 'app-root',
    templateUrl: 'app.component.html',
    imports: [IonApp, IonRouterOutlet],
})
export class AppComponent {
    readonly leagueStore = inject(LeaguesStore);
    readonly teamsStore = inject(TeamsStore);
    loadingCtrl = inject(LoadingController);
    private loadingSpinner: HTMLIonLoadingElement | undefined;

    storesIsLoading = ;

    rxStoreIsLoading = toObservable(computed(() =>
        this.leagueStore.status() === 'busy' || this.teamsStore.status() === 'busy'
    ))





    constructor() {
        /* effect(() => {
            if (
                this.leagueStore.status() === 'busy' ||
                this.teamsStore.status() === 'busy'
            ) {
                this.showLoading();
            } else {
                this.hideLoading();
            }
        });*/

        this.loading$
            .pipe(distinctUntilChanged(), startWith(false))
            .subscribe((t) => {
                if (t.) {
                    this.showLoading();
                } else {
                    this.hideLoading();
                }
            });
    }

    async showLoading() {
        this.loadingSpinner = await this.loadingCtrl.create({
            message: 'Loading...',
        });

        await this.loadingSpinner.present();
    }

    private hideLoading() {
        if (this.loadingSpinner) {
            this.loadingSpinner.dismiss();
        }
    }
}
